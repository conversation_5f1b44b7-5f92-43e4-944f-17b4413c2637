package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CreateCardCollectionRequest;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
//import com.ously.gamble.collectibles.persistence.repository.CardRepository;
//import com.ously.gamble.collectibles.persistence.repository.RewardRepository;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
@Transactional
public class CollectionService {

    private final CardCollectionRepository cardCollectionRepository;
//    private final CardRepository cardRepository;
//    private final RewardRepository rewardRepository;

    public CollectionService(CardCollectionRepository cardCollectionRepository
//                           CardRepository cardRepository,
//                           RewardRepository rewardRepository) {
                           ) {
        this.cardCollectionRepository = cardCollectionRepository;
//        this.cardRepository = cardRepository;
//        this.rewardRepository = rewardRepository;
    }

    // === COLLECTION OPERATIONS ===

//    @Transactional(readOnly = true)
//    public Page<CardCollection> findAll(Pageable pageable) {
//        return cardCollectionRepository.findAll(pageable);
//    }
//
//    @Transactional(readOnly = true)
//    public Optional<CardCollection> findByIdWithCardsAndRewards(Integer id) {
//        return cardCollectionRepository.findByIdWithCardsAndRewards(id);
//    }
//
//    @Transactional(readOnly = true)
//    public Page<CardCollection> findActiveCollections(Pageable pageable) {
//        return cardCollectionRepository.findActiveCollections(LocalDateTime.now(), pageable);
//    }
//
//    @Transactional(readOnly = true)
//    public Optional<CardCollection> findActiveCollectionById(Integer id) {
//        return cardCollectionRepository.findActiveCollectionById(id, LocalDateTime.now());
//    }

    public CardCollection create(CreateCardCollectionRequest request) {
        CardCollection collection = new CardCollection();
        collection.setName(request.name());

        if (request.startDate() != null) {
            collection.setStartDate(request.startDate());
        }

        if (request.endDate() != null) {
            collection.setEndDate(request.endDate());
        }

        collection.setStatus(CardCollection.CollectionStatus.DISABLED);
        collection.setSortOrder(request.sortOrder() != null ? request.sortOrder() : 99);
        
        return cardCollectionRepository.save(collection);
    }

//    public Optional<CardCollection> update(Integer id, CardCollectionDto.UpdateCardCollectionRequest request) {
//        return cardCollectionRepository.findById(id)
//                .map(collection -> {
//                    if (request.name() != null) {
//                        validateCollectionName(request.name(), id);
//                        collection.setName(request.name());
//                    }
//                    if (request.description() != null) {
//                        collection.setDescription(request.description());
//                    }
//                    if (request.startDate() != null) {
//                        collection.setStartDate(request.startDate());
//                    }
//                    if (request.endDate() != null) {
//                        collection.setEndDate(request.endDate());
//                    }
//                    if (request.status() != null) {
//                        collection.setStatus(request.status());
//                    }
//                    if (request.sortOrder() != null) {
//                        collection.setSortOrder(request.sortOrder());
//                    }
//                    return cardCollectionRepository.save(collection);
//                });
//    }
//
//    public boolean delete(Integer id) {
//        if (cardCollectionRepository.existsById(id)) {
//            cardCollectionRepository.deleteById(id);
//            return true;
//        }
//        return false;
//    }
//
//    // === CARD OPERATIONS ===
//
//    public CardCollection createCard(Integer collectionId, CardCollectionDto.CreateCardRequest request) {
//        CardCollection collection = cardCollectionRepository.findById(collectionId)
//                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
//
//        Card card = new Card();
//        card.setCardCollection(collection);
//        card.setName(request.name());
//        card.setImageUrl(request.imageUrl());
//        card.setRarityLevel(request.rarityLevel());
//        card.setStartDate(request.startDate());
//        card.setEndDate(request.endDate());
//        card.setSortOrder(request.sortOrder());
//        card.setStatus(Card.CardStatus.DISABLED);
//
//        cardRepository.save(card);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection updateCard(Integer collectionId, Integer cardId, CardCollectionDto.UpdateCardRequest request) {
//        Card card = cardRepository.findById(cardId)
//                .orElseThrow(() -> new IllegalArgumentException("Card not found: " + cardId));
//
//        if (!card.getCardCollection().getId().equals(collectionId)) {
//            throw new IllegalArgumentException("Card does not belong to collection: " + collectionId);
//        }
//
//        if (request.name() != null) card.setName(request.name());
//        if (request.imageUrl() != null) card.setImageUrl(request.imageUrl());
//        if (request.rarityLevel() != null) card.setRarityLevel(request.rarityLevel());
//        if (request.startDate() != null) card.setStartDate(request.startDate());
//        if (request.endDate() != null) card.setEndDate(request.endDate());
//        if (request.status() != null) card.setStatus(request.status());
//        if (request.sortOrder() != null) card.setSortOrder(request.sortOrder());
//
//        cardRepository.save(card);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection deleteCard(Integer collectionId, Integer cardId) {
//        Card card = cardRepository.findById(cardId)
//                .orElseThrow(() -> new IllegalArgumentException("Card not found: " + cardId));
//
//        if (!card.getCardCollection().getId().equals(collectionId)) {
//            throw new IllegalArgumentException("Card does not belong to collection: " + collectionId);
//        }
//
//        cardRepository.deleteById(cardId);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    // === REWARD OPERATIONS ===
//
//    public CardCollection createReward(Integer collectionId, CardCollectionDto.CreateRewardRequest request) {
//        CardCollection collection = cardCollectionRepository.findById(collectionId)
//                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
//
//        Reward reward = new Reward();
//
//        if (request.cardId() != null) {
//            Card card = cardRepository.findById(request.cardId())
//                    .orElseThrow(() -> new IllegalArgumentException("Card not found: " + request.cardId()));
//            if (!card.getCardCollection().getId().equals(collectionId)) {
//                throw new IllegalArgumentException("Card does not belong to collection: " + collectionId);
//            }
//            reward.setCard(card);
//        } else {
//            reward.setCardCollection(collection);
//        }
//
//        reward.setRewardType(request.rewardType());
//        reward.setMilestonePercentage(request.milestonePercentage());
//        reward.setRewardData(request.rewardData());
//
//        rewardRepository.save(reward);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection updateReward(Integer collectionId, Integer rewardId, CardCollectionDto.UpdateRewardRequest request) {
//        Reward reward = rewardRepository.findById(rewardId)
//                .orElseThrow(() -> new IllegalArgumentException("Reward not found: " + rewardId));
//
//        // Verify reward belongs to collection
//        boolean belongsToCollection = (reward.getCardCollection() != null && reward.getCardCollection().getId().equals(collectionId)) ||
//                                    (reward.getCard() != null && reward.getCard().getCardCollection().getId().equals(collectionId));
//
//        if (!belongsToCollection) {
//            throw new IllegalArgumentException("Reward does not belong to collection: " + collectionId);
//        }
//
//        if (request.milestonePercentage() != null) {
//            reward.setMilestonePercentage(request.milestonePercentage());
//        }
//        if (request.rewardData() != null) {
//            reward.setRewardData(request.rewardData());
//        }
//
//        rewardRepository.save(reward);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    public CardCollection deleteReward(Integer collectionId, Integer rewardId) {
//        Reward reward = rewardRepository.findById(rewardId)
//                .orElseThrow(() -> new IllegalArgumentException("Reward not found: " + rewardId));
//
//        // Verify reward belongs to collection
//        boolean belongsToCollection = (reward.getCardCollection() != null && reward.getCardCollection().getId().equals(collectionId)) ||
//                                    (reward.getCard() != null && reward.getCard().getCardCollection().getId().equals(collectionId));
//
//        if (!belongsToCollection) {
//            throw new IllegalArgumentException("Reward does not belong to collection: " + collectionId);
//        }
//
//        rewardRepository.deleteById(rewardId);
//        return findByIdWithCardsAndRewards(collectionId).orElseThrow();
//    }
//
//    // === VALIDATION ===
//
//    private void validateCollectionName(String name, Integer excludeId) {
//        boolean exists = cardCollectionRepository.findByNameIgnoreCase(name.trim()).stream()
//                .anyMatch(c -> excludeId == null || !c.getId().equals(excludeId));
//        if (exists) {
//            throw new IllegalArgumentException("Collection with this name already exists: " + name);
//        }
//    }
}
